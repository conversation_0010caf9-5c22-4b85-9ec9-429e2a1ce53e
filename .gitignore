# =============================================================================
# .gitignore for Solana Trading Bot (Node.js + TypeScript + pnpm)
# =============================================================================

# =============================================================================
# Node.js & npm/pnpm/yarn
# =============================================================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager lock files (keep pnpm-lock.yaml for this project)
# npm-shrinkwrap.json
# package-lock.json
# yarn.lock

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# =============================================================================
# Environment & Configuration Files
# =============================================================================
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Configuration files with sensitive data
config.json5
config.json
config.yaml
config.yml
.config/
secrets/
credentials/

# =============================================================================
# Build & Distribution
# =============================================================================
# Build outputs
dist/
build/
out/
lib/
*.js.map
*.d.ts.map

# SWC cache
.swc/

# =============================================================================
# Database & Storage
# =============================================================================
# SQLite databases
*.sqlite
*.sqlite3
*.db
*.db3

# Database backups
*.sql.gz
*.sql.bak

# Storage directory (logs, data, cache)
storage/
data/
cache/

# =============================================================================
# Logs & Temporary Files
# =============================================================================
# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.swp
*.swo
*~

# =============================================================================
# IDE & Editor Files
# =============================================================================
# VSCode (keep settings for team consistency)
# .vscode/

# WebStorm/IntelliJ
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.vimrc.local

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Operating System Files
# =============================================================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Blockchain & Crypto Specific
# =============================================================================
# Private keys and wallets
*.key
*.pem
*.p12
*.pfx
wallet.json
keypair.json
private-key*
mnemonic*

# Solana CLI config (may contain sensitive data)
.config/solana/

# =============================================================================
# Testing & Coverage
# =============================================================================
# Test results
test-results/
coverage/
.nyc_output/
junit.xml

# =============================================================================
# Miscellaneous
# =============================================================================
# Runtime files
*.pid
*.seed
*.pid.lock

# Optional cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =============================================================================
# Project Specific Ignores
# =============================================================================
# Add any project-specific files or directories here
# Example: custom build outputs, generated files, etc.
