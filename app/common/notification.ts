import { tap } from '@kdt310722/utils/function'
import { config } from '../config'
import { logger } from '../core/logger'
import { Notification } from '../modules/notification/notification'
import { onBuyFailed, onBuySuccess } from './buy'
import { onPaymentTransaction } from './dexscreener'

export const notification = new Notification(config.notification)

notification.on('error', (error) => {
    logger.error(error)
})

export async function initializeNotification() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing notification module...'))

    onBuySuccess((result, params) => notification.sendBuyNotification({ transaction: result, params }))
    onBuyFailed((result, params) => notification.sendBuyNotification({ transaction: result, params }))
    onPaymentTransaction((transaction, pools) => notification.sendPaymentTransactionNotification({ transaction, pools }))

    await Promise.resolve().then(() => {
        logger.stopTimer(timer, 'info', 'Notification module is initialized!')
    })
}
