import type Decimal from 'decimal.js'
import type { DexScreenerPaymentTransaction } from '../types/tracker'
import type { RaydiumLaunchPadPool } from '../utils/raydium/pools'
import { highlight } from '@kdt310722/logger'
import { shorten } from '@kdt310722/utils/string'
import { ConstantProductCurve } from '@kdt-bun/raydium-launchpad-sdk'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { getSolPrice } from './indexer'
import { onTransaction, type TrackerTransactionHandler } from './tracker'

export interface FilteredPool extends RaydiumLaunchPadPool {
    price: Decimal
    marketCap: Decimal
    holdingTokens: bigint
}

function getAvailableToBuyPools(pools: RaydiumLaunchPadPool[], holdingTokens: Record<string, bigint>) {
    const filteredPools: FilteredPool[] = []

    for (const pool of pools.toReversed()) {
        if (pool.status !== 0 || holdingTokens[pool.baseMint] < config.buy.minHolding) {
            continue
        }

        const price = ConstantProductCurve.calculatePoolPrice(pool, pool.baseDecimals, pool.quoteDecimals)
        const marketCap = price.mul(getSolPrice()).mul(pool.supply / BigInt(10 ** pool.baseDecimals))

        if (marketCap.lt(config.buy.minMarketCap) || marketCap.gt(config.buy.maxMarketCap)) {
            continue
        }

        filteredPools.push({ ...pool, price, marketCap, holdingTokens: holdingTokens[pool.baseMint] })
    }

    return filteredPools
}

const logger = createChildLogger('common:dexscreener')
const transactionHandlers = new Set<TrackerTransactionHandler>()
const availableToBuyPoolHandlers = new Set<(pools: FilteredPool[], transaction: DexScreenerPaymentTransaction) => void>()

export function onPaymentTransaction(handler: TrackerTransactionHandler) {
    transactionHandlers.add(handler)
}

export function onAvailableToBuyPools(handler: (pools: FilteredPool[], transaction: DexScreenerPaymentTransaction) => void) {
    availableToBuyPoolHandlers.add(handler)
}

const handlePaymentTransaction: TrackerTransactionHandler = (transaction, pools, holdingTokens) => {
    const printIgnore = () => logger.info(`No tokens to buy for transaction ${highlight(shorten(transaction.signature, 4))}`)

    if (pools.length > 0) {
        const availableToBuyPools = getAvailableToBuyPools(pools, holdingTokens)

        if (availableToBuyPools.length > 0) {
            for (const handler of availableToBuyPoolHandlers) {
                handler(availableToBuyPools, transaction)
            }
        } else {
            printIgnore()
        }
    } else {
        printIgnore()
    }

    for (const handler of transactionHandlers) {
        handler(transaction, pools, holdingTokens)
    }
}

export async function initializeDexScreener() {
    await Promise.resolve().then(() => onTransaction(handlePaymentTransaction))
}
