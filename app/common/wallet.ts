import { highlight, message } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { shorten } from '@kdt310722/utils/string'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { rpcHttpClient, rpcSubscriptions } from '../core/rpc-client'
import { Wallet } from '../modules/wallet/wallet'
import { formatSol } from '../utils/formatters/format-sol'

const logger = createChildLogger('common:wallet')

export const wallet = new Wallet(rpcHttpClient, rpcSubscriptions, config.wallet.privateKey, config.wallet.nonceAccount)

wallet.on('error', (error) => logger.error('Wallet module error', error))
wallet.on('balance', (balance) => logger.info(`Current wallet balance: ${highlight(formatSol(balance))}`))
wallet.on('logs', ({ signature }) => logger.debug(message(() => `New wallet transaction: ${highlight(shorten(signature, 4))}`)))

wallet.tokenAccount.on('add', ({ mint, amount }) => logger.debug(message(() => `New token account: ${highlight(`mint=${mint}`)}, ${highlight(`amount=${amount}`)}`)))
wallet.tokenAccount.on('update', ({ mint, amount }) => logger.debug(message(() => `Token account updated: ${highlight(`mint=${mint}`)}, ${highlight(`amount=${amount}`)}`)))
wallet.tokenAccount.on('remove', ({ mint }) => logger.debug(message(() => `Token account removed: ${highlight(`mint=${mint}`)}`)))
wallet.tokenAccount.on('error', (error) => logger.error('Token account error', error))

wallet.nonce.on('error', (error) => logger.error('Nonce error', error))
wallet.nonce.on('update', (data) => logger.info(`Current nonce: ${highlight(data.nonce)}`))

export async function initializeWallet() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing wallet module...'))

    await wallet.init().then(() => {
        logger.stopTimer(timer, 'info', `Wallet: ${highlight(shorten(wallet.signer.address, 4))}, Token Accounts: ${highlight(format(wallet.tokenAccount.tokenAccounts.length))}`)
    })
}
