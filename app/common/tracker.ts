import type { Repository } from 'typeorm'
import type { DexScreenerPaymentTransaction } from '../types/tracker'
import { highlight, LogLevel } from '@kdt310722/logger'
import { RpcWebSocketClient } from '@kdt310722/rpc'
import { LruSet } from '@kdt310722/utils/array'
import { join } from '@kdt310722/utils/buffer'
import { isEmpty } from '@kdt310722/utils/common'
import { tap, transform } from '@kdt310722/utils/function'
import { address, type Address } from '@solana/kit'
import { config } from '../config'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { PaymentTransaction } from '../entities/payment-transaction'
import { toPaymentTransactionEntity } from '../utils/formatters/to-payment-transaction-entity'
import { findRaydiumLaunchPadPools, type RaydiumLaunchPadPool } from '../utils/raydium/pools'
import { getTokensByOwner } from '../utils/solana/wallet'

const logger = createChildLogger('common:tracker')
const tracker = new RpcWebSocketClient(config.tracker.url, config.tracker)
const trackedTokens = new LruSet<string>(10_000)

let repository: Repository<PaymentTransaction> | undefined

function getRepository() {
    return repository ??= database.getRepository(PaymentTransaction)
}

export type TrackerTransactionHandler = (transaction: DexScreenerPaymentTransaction, pools: RaydiumLaunchPadPool[], holdingTokens: Record<string, bigint>) => void

const transactionHandlers = new Set<TrackerTransactionHandler>()

export function onTransaction(handler: TrackerTransactionHandler) {
    transactionHandlers.add(handler)
}

async function handlePaymentNotification(data: DexScreenerPaymentTransaction) {
    const filter = (mint: Address) => {
        if (trackedTokens.has(mint)) {
            return false
        }

        return tap(true, () => trackedTokens.add(mint))
    }

    const tokenAccounts = await getTokensByOwner(address(data.payer))
    const mints = tokenAccounts.filter((i) => filter(i.mint)).map(({ mint }) => mint)
    const pools = await findRaydiumLaunchPadPools(mints)
    const holdingTokens = Object.fromEntries(tokenAccounts.map(({ mint, amount }) => [mint, amount]))

    for (const handler of transactionHandlers) {
        handler(data, pools, holdingTokens)
    }

    await getRepository().save(toPaymentTransactionEntity(data, tokenAccounts.map(({ mint }) => mint)))
}

let isInitialized = false

tracker.socket.on('reconnect', (attempts) => logger.info(`Reconnecting to DexScreener Payment Tracker server (attempts: ${highlight(attempts)})...`))
tracker.socket.on('error', (error) => logger.error('Error occurred in DexScreener Payment Tracker server', error))
tracker.on('error', (error) => logger.error('Error occurred in DexScreener Payment Tracker server', error))
tracker.on('unhandledMessage', (message) => logger.warn('Received unhandled message from DexScreener Payment Tracker server', message))
tracker.on('unhandledRpcMessage', (message) => logger.warn('Received unhandled message from DexScreener Payment Tracker server', message))

tracker.on('notification', (method, params) => {
    if (method === 'payment') {
        handlePaymentNotification(params).catch((error) => {
            logger.error('Failed to handle payment notification', error, { transaction: params })
        })
    }
})

tracker.socket.on('connected', () => {
    tracker.socket.resetRetryCount()

    if (isInitialized) {
        logger.info('Connected to DexScreener Payment Tracker server!')
    }
})

tracker.socket.on('disconnected', (code, reason, isExplicitlyClosed) => {
    const message = `Disconnected from DexScreener Payment Tracker server: ${highlight(`${code} - ${transform(join(reason), (r) => (isEmpty(r) ? 'EMPTY_REASON' : r))}`)}`

    if (tracker.socket.isReconnectAttemptReached) {
        logger.exit(1, 'fatal', message)
    } else if (isInitialized) {
        logger.log(isExplicitlyClosed ? LogLevel.INFO : LogLevel.WARN, message)
    }
})

async function loadTrackedTokens() {
    const transactions = await getRepository().find({ select: ['tokens'] })

    for (const { tokens } of transactions) {
        for (const mint of tokens) {
            trackedTokens.add(mint)
        }
    }
}

export async function initializeTracker() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing tracker module...'))

    await loadTrackedTokens().then(() => tracker.socket.connect()).then(() => isInitialized = true).then(() => {
        logger.stopTimer(timer, 'info', 'Tracker module is initialized!')
    })
}
