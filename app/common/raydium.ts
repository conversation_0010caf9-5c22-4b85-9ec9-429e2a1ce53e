import { highlight } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { rpcHttpClient, rpcSubscriptions } from '../core/rpc-client'
import { Raydium } from '../modules/raydium/raydium'
import { wallet } from './wallet'

const logger = createChildLogger('common:raydium')

export const raydium = new Raydium(rpcHttpClient, rpcSubscriptions, config.raydium.globalConfigAddress, config.raydium.platformConfigAddress, wallet)

let currentFee: bigint | undefined

raydium.on('fee', (fee) => {
    if (currentFee === fee) {
        return
    }

    logger.info(`Current Raydium trade fee: ${highlight(`${format(Number(currentFee = fee) / 10_000, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}%`)}`)
})

export async function initializeRaydium() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing Raydium module...'))

    await raydium.init().then(() => {
        logger.stopTimer(timer, 'info', 'Raydium module is initialized!')
    })
}
