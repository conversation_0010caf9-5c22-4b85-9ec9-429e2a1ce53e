import type { PoolState } from '@kdt-bun/raydium-launchpad-sdk'
import type { TradeParam } from '../modules/raydium/trade'
import type { GetTransactionResponse } from '../utils/solana/transactions'
import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { rpcHttpClient, rpcSendClient } from '../core/rpc-client'
import { confirmTransaction } from '../utils/solana/confirm-transaction'
import { signAndSerializeTransaction } from '../utils/solana/transaction-messages'
import { onAvailableToBuyPools } from './dexscreener'
import { raydium } from './raydium'
import { wallet } from './wallet'

const logger = createChildLogger('common:buy')

export const getDefaultBuyParams = (pool: PoolState): TradeParam => ({
    pool,
    amount: config.buy.amount,
    slippage: config.buy.slippage,
    priorityFee: config.buy.priorityFee,
})

export type BuyResultHandler = (result: GetTransactionResponse, params: TradeParam, metadata: Record<string, any>) => void

const buySuccessHandlers = new Set<BuyResultHandler>()
const buyFailedHandlers = new Set<BuyResultHandler>()

export function onBuySuccess(handler: BuyResultHandler) {
    buySuccessHandlers.add(handler)
}

export function onBuyFailed(handler: BuyResultHandler) {
    buyFailedHandlers.add(handler)
}

export interface BuyOptions {
    timeout?: number
    metadata?: Record<string, any>
}

export async function buy(params: TradeParam, { timeout = config.buy.timeout, metadata = {} }: BuyOptions = {}) {
    logger.info('Buying token...', { mint: params.pool.baseMint })

    const message = await raydium.trade.getBuyTransaction(params)
    const tx = await signAndSerializeTransaction(wallet.signer.keyPair, message)
    const signature = await rpcSendClient.sendTransaction(tx, { encoding: 'base64', skipPreflight: true }).send()

    try {
        const result = await confirmTransaction(rpcHttpClient, wallet, signature, timeout)

        if (notNullish(result.meta?.err)) {
            logger.warn('Buy failed', { signature, mint: params.pool.baseMint, err: result.meta.err })

            for (const handler of buyFailedHandlers) {
                handler(result, params, metadata)
            }
        } else {
            logger.info('Buy succeeded', { signature, mint: params.pool.baseMint })

            for (const handler of buySuccessHandlers) {
                handler(result, params, metadata)
            }
        }
    } catch (error) {
        logger.error('Failed to confirm transaction', error, { signature, mint: params.pool.baseMint })
    }
}

export async function initializeBuy() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing buy module...'))

    onAvailableToBuyPools((pools, transaction) => {
        if (!config.buy.enabled) {
            return logger.info('Buy is disabled')
        }

        if (wallet.balance <= config.buy.minBalance) {
            return logger.info('Not enough balance to buy', { balance: wallet.balance, minBalance: config.buy.minBalance })
        }

        buy(getDefaultBuyParams(pools[0]), { metadata: { transaction } }).catch((error) => {
            logger.error('Failed to buy token', error, { mint: pools[0].baseMint, transaction: transaction.signature })
        })
    })

    await Promise.resolve().then(() => {
        logger.stopTimer(timer, 'info', 'Buy module is initialized!')
    })
}
