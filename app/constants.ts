import { getTokenSize } from '@solana-program/token'
import { address } from '@solana/kit'

export const NATIVE_MINT = address('So11111111111111111111111111111111111111112')

export const RAYDIUM_LAUNCHPAD_DEFAULT_QUOTE_MINT = NATIVE_MINT

export const SOL_DECIMALS = 9

export const SOL_DENOMINATOR = 10 ** SOL_DECIMALS

export const QUOTE_TOKEN_DECIMALS = SOL_DECIMALS

export const QUOTE_TOKEN_DENOMINATOR = SOL_DENOMINATOR

export const BASE_TOKEN_DECIMALS = 6

export const BASE_TOKEN_DENOMINATOR = 10 ** BASE_TOKEN_DECIMALS

export const TOKEN_ACCOUNT_SIZE = BigInt(getTokenSize())

export const MICRO_LAMPORTS_PER_LAMPORT = 10n ** 6n
