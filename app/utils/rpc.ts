import { createRequestMessage, isJsonRpcErrorResponseMessage, isJsonRpcMessage, isJsonRpcResponseMessage, toJsonRpcError } from '@kdt310722/rpc'
import { tryCatch } from '@kdt310722/utils/function'
import { parseJson } from '@kdt310722/utils/json'
import { fetch, type FetchOptions } from '@kdt310722/utils/node'

export interface RpcCallOptions extends FetchOptions {
    id?: string | number
    headers?: Record<string, string>
}

export async function call<TResult = unknown, TPayload = unknown>(url: string, method: string, params?: TPayload, { id = '1', headers = {}, ...options }: RpcCallOptions = {}): Promise<TResult> {
    const payload = createRequestMessage(id, method, params)
    const request = { method: 'POST', headers: { 'Content-Type': 'application/json', ...headers }, body: JSON.stringify(payload) }
    const response = await fetch(url, request, options)
    const body = await response.text()
    const createError = (message: string, cause?: unknown) => Object.assign(new Error(message, { cause }), { request, response: { statusCode: response.status, statusText: response.statusText, body, headers: Object.fromEntries(response.headers) } })
    const data = tryCatch(() => parseJson(body), body)

    if (!isJsonRpcMessage(data) || !isJsonRpcResponseMessage(data)) {
        throw createError('Invalid JSON-RPC response')
    }

    if (isJsonRpcErrorResponseMessage(data)) {
        throw toJsonRpcError(data.error)
    }

    return data.result
}
