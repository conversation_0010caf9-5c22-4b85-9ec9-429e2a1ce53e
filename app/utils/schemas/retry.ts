import { isBoolean } from '@kdt310722/utils/common'
import { z } from 'zod'
import { bool } from './bool'
import { nullish } from './nullish'

const schema = z.object({
    enabled: nullish(bool),
    delay: nullish(z.number().int().nonnegative()),
    retries: nullish(z.number().int().nonnegative()),
})

export const retry = z.union([z.boolean(), schema.default({})]).default(true).transform((val) => {
    return isBoolean(val) ? schema.parse({ enabled: val }) : val
})
