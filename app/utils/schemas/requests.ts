import { isHttpUrl, isWebSocketUrl } from '@kdt310722/utils/string'
import { z } from 'zod'

export const httpUrl = z.string().refine((value) => isHttpUrl(value), 'Must be a valid HTTP URL')

export const wsUrl = z.string().refine((value) => isWebSocketUrl(value), 'Must be a valid WebSocket URL')

export const httpMethod = z.string().transform((value) => value.toUpperCase()).pipe(z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS', 'TRACE', 'CONNECT']))

export const httpHeaders = z.record(z.string(), z.string())
