import { z } from 'zod'
import { BASE_TOKEN_DENOMINATOR, QUOTE_TOKEN_DENOMINATOR, SOL_DENOMINATOR } from '../../constants'
import { toBigInt } from '../formatters/to-bigint'

export const solAmount = z.coerce.number().safe().finite().transform((val) => toBigInt(val * SOL_DENOMINATOR))

export const quoteTokenAmount = z.coerce.number().safe().finite().transform((val) => toBigInt(val * QUOTE_TOKEN_DENOMINATOR))

export const baseTokenAmount = z.coerce.number().safe().finite().transform((val) => toBigInt(val * BASE_TOKEN_DENOMINATOR))
