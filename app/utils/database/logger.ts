import type { LogLevel as DatabaseLogLevel, LoggerOptions, LogMessage } from 'typeorm'
import { type Logger, LogLevel } from '@kdt310722/logger'
import { AbstractLogger } from 'typeorm'

export const levels = <const>{
    'query': LogLevel.DEBUG,
    'query-error': LogLevel.ERROR,
    'query-slow': LogLevel.WARN,
    'schema': LogLevel.INFO,
    'schema-build': LogLevel.DEBUG,
    'error': LogLevel.ERROR,
    'warn': LogLevel.WARN,
    'info': LogLevel.DEBUG,
    'log': LogLevel.INFO,
    'migration': LogLevel.INFO,
}

export class DatabaseLogger extends AbstractLogger {
    public constructor(protected readonly logger: Logger, options?: LoggerOptions) {
        super(options)
    }

    protected override writeLog(level: DatabaseLogLevel, message: LogMessage | string | number) {
        const messages = this.prepareLogMessages(message)

        for (const msg of messages) {
            const logMessage = msg.message.toString()
            const logType = msg.type ?? level
            const logLevel = levels[logType] ?? LogLevel.INFO
            const logTypeStr = String(logType).toUpperCase()

            if (logLevel === LogLevel.DEBUG && logMessage.includes('/* ignore_query_log */')) {
                continue
            }

            this.logger.log(logLevel, `[${logTypeStr}] ${msg.prefix?.length ? `${msg.prefix} ` : ''}${logMessage}`)
        }
    }
}
