import type { Address } from '@solana/kit'
import { chunk } from '@kdt310722/utils/array'
import { fetchAllMaybePoolState, getRaydiumLaunchpadPoolId, type PoolState } from '@kdt-bun/raydium-launchpad-sdk'
import { RAYDIUM_LAUNCHPAD_DEFAULT_QUOTE_MINT } from '../../constants'
import { rpcHttpClient } from '../../core/rpc-client'

export interface RaydiumLaunchPadPool extends PoolState {
    address: Address
}

export async function findRaydiumLaunchPadPools(mints: Address[]) {
    if (mints.length === 0) {
        return []
    }

    const result: RaydiumLaunchPadPool[] = []
    const chunks = chunk(mints, 100)

    const find = async (mints: Address[]) => {
        const poolIds = await Promise.all(mints.map((mint) => getRaydiumLaunchpadPoolId(mint, RAYDIUM_LAUNCHPAD_DEFAULT_QUOTE_MINT)))
        const poolStates = await fetchAllMaybePoolState(rpcHttpClient, poolIds)

        for (const poolState of poolStates) {
            if (poolState.exists) {
                result.push({ address: poolState.address, ...poolState.data })
            }
        }
    }

    const promises: Array<Promise<void>> = []

    for (const mintsChunk of chunks) {
        promises.push(find(mintsChunk))
    }

    return Promise.all(promises).then(() => result)
}
