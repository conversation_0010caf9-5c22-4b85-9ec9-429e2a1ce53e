import type { Address } from '@solana/kit'
import type { Wallet } from '../../modules/wallet/wallet'

export async function prepareForTrade(wallet: Wallet, mint: Address, amount: bigint) {
    const { address: quoteTokenAccount, instructions: createQuoteTokenAccountInstructions } = await wallet.getCreateWSolTokenAccountInstructions(amount)
    const { address: baseTokenAccount, instructions: createBaseTokenAccountInstructions } = await wallet.getCreateTokenAccountInstructions(mint)

    const preInstructions = [...createQuoteTokenAccountInstructions, ...createBaseTokenAccountInstructions]
    const postInstructions = wallet.getCloseTokenAccountInstructions(quoteTokenAccount)

    return { quoteTokenAccount, baseTokenAccount, preInstructions, postInstructions }
}
