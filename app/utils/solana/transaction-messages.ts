import { transform } from '@kdt310722/utils/function'
import { getSetComputeUnitLimitInstruction, getSetComputeUnitPriceInstruction } from '@solana-program/compute-budget'
import { type AccountMeta, appendTransactionMessageInstructions, type BaseTransactionMessage, type CompilableTransactionMessage, compileTransaction, getBase64EncodedWireTransaction, signTransaction } from '@solana/kit'
import { MICRO_LAMPORTS_PER_LAMPORT } from '../../constants'

export interface PriorityFee {
    units: number
    lamports: bigint
    accounts?: AccountMeta[]
}

export function setTransactionMessagePriorityFee<TTransaction extends BaseTransactionMessage>({ units, lamports, accounts = [] }: PriorityFee, tx: TTransaction) {
    const microLamports = BigInt(Math.trunc(Number(lamports) / units * Number(MICRO_LAMPORTS_PER_LAMPORT)))
    const instructions = [transform(getSetComputeUnitLimitInstruction({ units }), (i) => ({ ...i, accounts: [...(i.accounts ?? []), ...accounts] })), getSetComputeUnitPriceInstruction({ microLamports })]

    return appendTransactionMessageInstructions(instructions, tx)
}

export async function signAndSerializeTransaction<TTransactionMessage extends CompilableTransactionMessage>(keyPair: CryptoKeyPair, message: TTransactionMessage) {
    return getBase64EncodedWireTransaction(await signTransaction([keyPair], compileTransaction(message)))
}
