import type { AccountMeta, Base58EncodedBytes, Instruction, InstructionWithAccounts, InstructionWithData } from '@solana/kit'
import type { GetTransactionResponse } from './transactions'
import { isString } from '@kdt310722/utils/string'
import base58 from 'bs58'

export type RawInstruction = Omit<GetTransactionResponse['transaction']['message']['instructions'][0], 'data'> & {
    data: Uint8Array | Base58EncodedBytes
}

export const parseInstruction = (accounts: AccountMeta[], instruction: RawInstruction): Instruction & InstructionWithAccounts<AccountMeta[]> & InstructionWithData<Uint8Array> => ({
    accounts: instruction.accounts.map((index) => accounts[index]),
    data: isString(instruction.data) ? base58.decode(instruction.data) : instruction.data,
    programAddress: accounts[instruction.programIdIndex].address,
})
