import { isNullish, notNullish } from '@kdt310722/utils/common'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type RetryOptions, withRetry } from '@kdt310722/utils/promise'
import { type AccountMeta, AccountRole, type Commitment, type Rpc, type Signature, type SolanaRpcApiTestnet } from '@solana/kit'

export type GetTransactionResponse = Awaited<ReturnType<typeof getTransaction>>

export interface GetTransactionOptions {
    commitment?: Commitment
    retry?: (RetryOptions & { enabled?: boolean }) | boolean
    signal?: AbortSignal
}

export const isTransactionNotFoundError = (error: unknown) => error instanceof Error && error.message === 'Transaction not found'

export async function getTransaction(client: Rpc<SolanaRpcApiTestnet>, signature: Signature, { commitment = 'confirmed', retry = true, signal }: GetTransactionOptions = {}) {
    const { enabled: retryEnabled = true, delay: retryDelay = 1000, ...retryOptions } = resolveNestedOptions(retry) || { enabled: false }

    const execute = async () => {
        const transaction = await client.getTransaction(signature, { commitment, encoding: 'json', maxSupportedTransactionVersion: 0 }).send({ abortSignal: signal })

        if (isNullish(transaction)) {
            throw Object.assign(new Error('Transaction not found'), { signature, commitment })
        }

        return transaction
    }

    if (retryEnabled) {
        const shouldRetry: RetryOptions['shouldRetry'] = (error) => {
            if (isTransactionNotFoundError(error)) {
                return true
            }

            return retryOptions.shouldRetry?.(error) ?? false
        }

        return withRetry(execute, { ...retryOptions, delay: retryDelay, signal, shouldRetry })
    }

    return execute()
}

export function getInstructions(transaction: GetTransactionResponse) {
    return [...transaction.transaction.message.instructions, ...transaction.meta?.innerInstructions?.flatMap((innerInstruction) => innerInstruction.instructions) ?? []]
}

export function getAccountRole(isWritable: boolean, isSigner: boolean) {
    if (isSigner) {
        return isWritable ? AccountRole.WRITABLE_SIGNER : AccountRole.READONLY_SIGNER
    }

    return isWritable ? AccountRole.WRITABLE : AccountRole.READONLY
}

export function getTransactionAccounts(transaction: GetTransactionResponse): AccountMeta[] {
    const accountKeys = transaction.transaction.message.accountKeys
    const header = transaction.transaction.message.header
    const loadedAddresses = transaction.meta?.loadedAddresses

    const accounts = accountKeys.map((account, index): AccountMeta => {
        const isWritable = index < header.numRequiredSignatures - header.numReadonlySignedAccounts || (index >= header.numRequiredSignatures && index < accountKeys.length - header.numReadonlyUnsignedAccounts)
        const isSigner = index < header.numRequiredSignatures

        return { address: account, role: getAccountRole(isWritable, isSigner) }
    })

    if (notNullish(loadedAddresses)) {
        accounts.push(
            ...loadedAddresses.writable.map((i) => ({ address: i, role: AccountRole.WRITABLE })),
            ...loadedAddresses.readonly.map((i) => ({ address: i, role: AccountRole.READONLY })),
        )
    }

    return accounts
}
