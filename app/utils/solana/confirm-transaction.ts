import type { Rpc, Signature, SolanaRpcApiTestnet } from '@solana/kit'
import type { Wallet, WalletEvents } from '../../modules/wallet/wallet'
import { createDeferredWithTimeout } from '@kdt310722/utils/promise'
import { getTransaction, type GetTransactionOptions, type GetTransactionResponse, isTransactionNotFoundError } from './transactions'

export async function confirmTransaction(client: Rpc<SolanaRpcApiTestnet>, wallet: Wallet, signature: Signature, timeout: number) {
    const abortController = new AbortController()
    const signal = abortController.signal
    const transaction = createDeferredWithTimeout<GetTransactionResponse>(timeout, 'Transaction confirmation timed out')
    const errors: unknown[] = []

    const promises = [
        confirmTransactionUsingRpcHttp(client, signature, timeout, { signal }).then((tx) => transaction.resolve(tx)).catch((error) => errors.push(error)),
        confirmTransactionUsingWallet(client, wallet, signature, timeout, { signal }).then((tx) => transaction.resolve(tx)).catch((error) => errors.push(error)),
    ]

    Promise.all(promises).then(() => {
        if (!transaction.isSettled) {
            transaction.reject(new AggregateError(errors, 'All confirmation strategies failed'))
        }
    })

    return transaction.finally(() => {
        abortController.abort()
    })
}

export async function confirmTransactionUsingRpcHttp(client: Rpc<SolanaRpcApiTestnet>, signature: Signature, timeout: number, options: Omit<GetTransactionOptions, 'retry'> = {}) {
    options.signal?.throwIfAborted()

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    const abortSignal = AbortSignal.any([controller.signal, ...(options.signal ? [options.signal] : [])])

    try {
        while (true) {
            abortSignal.throwIfAborted()

            try {
                return await getTransaction(client, signature, { ...options, signal: abortSignal })
            } catch (error) {
                if (isTransactionNotFoundError(error)) {
                    continue
                }

                throw error
            }
        }
    } finally {
        clearTimeout(timeoutId)
    }
}

export interface ConfirmTransactionUsingWalletOptions {
    signal?: AbortSignal
}

export async function confirmTransactionUsingWallet(client: Rpc<SolanaRpcApiTestnet>, wallet: Wallet, signature: Signature, timeout: number, { signal }: ConfirmTransactionUsingWalletOptions = {}) {
    if (signal?.aborted) {
        throw signal.reason
    }

    const transaction = createDeferredWithTimeout<GetTransactionResponse>(timeout, 'Transaction confirmation timed out')

    let abortHandler: () => void
    let logsHandler: WalletEvents['logs']

    signal?.addEventListener('abort', abortHandler = () => {
        transaction.reject(signal.reason)
    })

    wallet.on('logs', logsHandler = ({ signature: txSignature }) => {
        if (txSignature === signature) {
            getTransaction(client, signature).then((tx) => transaction.resolve(tx)).catch((error) => {
                transaction.reject(error)
            })
        }
    })

    return transaction.finally(() => {
        signal?.removeEventListener('abort', abortHandler)
        wallet.off('logs', logsHandler)
    })
}
