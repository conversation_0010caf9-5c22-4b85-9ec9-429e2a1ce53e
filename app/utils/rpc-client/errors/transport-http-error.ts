import type { TransportRequest, TransportResponse } from '../types'
import { BaseError } from '@kdt310722/utils/error'
import { toTransportResponse } from '../utils'

export class TransportHttpError extends BaseError {
    public declare readonly request?: TransportRequest
    public declare readonly response?: TransportResponse

    public withRequest(request?: TransportRequest) {
        return this.withValue('request', request)
    }

    public withResponse(body: string, response: Response) {
        return this.withValue('response', toTransportResponse(body, response))
    }
}
