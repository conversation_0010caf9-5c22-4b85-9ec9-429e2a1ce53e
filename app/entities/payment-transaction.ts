import type { Address } from '@solana/kit'
import type { TokenAmount } from '../types/tracker'
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { JsonColumn } from '../utils/database/columns/json-column'

@Entity()
export class PaymentTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column()
    public declare slot: number

    @Column()
    public declare signature: string

    @Index()
    @Column()
    public declare payer: string

    @Index()
    @Column()
    public declare recipient: string

    @JsonColumn()
    public declare amount: TokenAmount

    @Index()
    @Column()
    public declare datasource: string

    @Column()
    public declare receivedAt: Date

    @Column({ nullable: true })
    public declare blockTime?: number

    @Column({ nullable: true })
    public declare nodeTime?: Date

    @JsonColumn()
    public declare tokens: Address[]

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date
}
