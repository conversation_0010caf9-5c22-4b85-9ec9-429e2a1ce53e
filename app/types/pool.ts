import type { PoolReserve } from '@kdt-bun/raydium-launchpad-sdk'
import type { Address } from '@solana/kit'

export interface Pool {
    address: Address
    baseMint: Address
    quoteMint: Address
    baseDecimals: number
    quoteDecimals: number
    baseVault: Address
    quoteVault: Address
    globalConfig: Address
    platformConfig: Address
    protocolFee: bigint
    platformFee: bigint
    reserves: PoolReserve
}
