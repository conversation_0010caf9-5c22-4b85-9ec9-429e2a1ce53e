import type { PoolReserve } from '@kdt-bun/raydium-launchpad-sdk'
import type { Address } from '@solana/kit'

export interface TradeAccounts {
    baseVault: Address
    quoteVault: Address
}

export interface PoolFees {
    protocolFee: bigint
    platformFee: bigint
    shareFee: bigint
}

export interface Trade {
    signature: string
    slot: number
    transactionIndex: number
    instructionIndex: number
    sortKey: bigint
    mint: Address
    poolId: Address
    user: Address
    isBuy: boolean
    accounts: TradeAccounts
    amountIn: bigint
    amountOut: bigint
    reserves: PoolReserve
    price: bigint
    fees: PoolFees
    globalConfig: Address
    platformConfig: Address
    timestamp: Date
}
