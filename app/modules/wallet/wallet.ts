import type { LogsNotification } from './types'
import { Emitter } from '@kdt310722/utils/event'
import { getCloseAccountInstruction, getCreateAssociatedTokenInstruction } from '@solana-program/token'
import { type Address, createKeyPairSignerFromBytes, getBase58Codec, type KeyPairSigner, type Rpc, type RpcSubscriptions, type SolanaRpcApiTestnet, type SolanaRpcSubscriptionsApi } from '@solana/kit'
import { Nonce } from './nonce'
import { TokenAccount } from './token-account'
import { getAssociatedTokenAddress } from './utils/addresses'
import { getCreateWSolTokenAccountInstructions } from './utils/instructions'

export type WalletEvents = {
    error: (error: unknown) => void
    balance: (balance: bigint) => void
    logs: (logs: LogsNotification) => void
}

export class Wallet extends Emitter<WalletEvents, true> {
    public readonly tokenAccount: TokenAccount
    public readonly nonce: Nonce

    protected readonly signal: AbortSignal

    #signer?: KeyPairSigner
    #balance?: bigint

    public constructor(protected readonly client: Rpc<SolanaRpcApiTestnet>, protected readonly subscriptions: RpcSubscriptions<SolanaRpcSubscriptionsApi>, protected readonly privateKey: string, nonceAddress: Address) {
        super()

        this.signal = new AbortController().signal
        this.tokenAccount = new TokenAccount(this, this.client, this.subscriptions, this.signal)
        this.nonce = new Nonce(this.client, this.subscriptions, nonceAddress)
    }

    public get signer() {
        if (!this.#signer) {
            throw new Error('Wallet is not initialized')
        }

        return this.#signer
    }

    public get balance() {
        if (!this.#balance) {
            throw new Error('Wallet is not initialized')
        }

        return this.#balance
    }

    public async init() {
        this.#signer = await createKeyPairSignerFromBytes(getBase58Codec().encode(this.privateKey))

        await this.watchBalance().then(() => this.updateBalance())
        await this.watchTransaction()
        await this.tokenAccount.init(this.signer.address)
        await this.nonce.init()
    }

    public getCloseTokenAccountInstructions(address: Address) {
        return [getCloseAccountInstruction({ account: address, destination: this.signer.address, owner: this.signer })]
    }

    public async getCreateTokenAccountInstructions(mint: Address) {
        if (this.tokenAccount.hasForMint(mint)) {
            return { address: this.tokenAccount.get(mint).address, instructions: [] }
        }

        const address = await getAssociatedTokenAddress(mint, this.signer.address)
        const instruction = getCreateAssociatedTokenInstruction({ mint, owner: this.signer.address, ata: address, payer: this.signer })

        return { address, instructions: [instruction] }
    }

    public async getCreateWSolTokenAccountInstructions(amount: bigint) {
        return getCreateWSolTokenAccountInstructions(this.signer, amount, this.tokenAccount.rentExempt)
    }

    protected async watchTransaction() {
        const logsNotifications = await this.subscriptions.logsNotifications({ mentions: [this.signer.address] }, { commitment: 'confirmed' }).subscribe({ abortSignal: this.signal })

        const handler = async () => {
            for await (const notification of logsNotifications) {
                this.emit('logs', notification.value)
            }
        }

        handler().catch((error) => {
            this.emit('error', error)
            this.watchTransaction()
        })
    }

    protected async getBalance() {
        return this.client.getBalance(this.signer.address, { commitment: 'confirmed' }).send().then(({ value }) => value)
    }

    protected async watchBalance() {
        const balanceNotifications = await this.subscriptions.accountNotifications(this.signer.address, { commitment: 'confirmed', encoding: 'base64' }).subscribe({ abortSignal: this.signal })

        const handler = async () => {
            for await (const notification of balanceNotifications) {
                this.setBalance(notification.value.lamports)
            }
        }

        handler().catch((error) => {
            this.emit('error', error)
            this.watchBalance().then(() => this.updateBalance())
        })
    }

    protected async updateBalance() {
        const balance = await this.getBalance()

        if (!this.#balance) {
            this.setBalance(balance)
        }
    }

    protected setBalance(balance: bigint) {
        this.emit('balance', this.#balance = balance)
    }
}
