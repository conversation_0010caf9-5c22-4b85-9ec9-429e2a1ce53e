import type { Account, Address, Base58EncodedBytes, Lamports, Rpc, RpcSubscriptions, SolanaRpcApiTestnet, SolanaRpcSubscriptionsApi } from '@solana/kit'
import type { LogsNotification, TokenAccount as TokenAccountType } from './types'
import type { Wallet } from './wallet'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { transform } from '@kdt310722/utils/function'
import { isKeyOf, pick } from '@kdt310722/utils/object'
import { decodeToken, TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import PQueue from 'p-queue'
import { TOKEN_ACCOUNT_SIZE } from '../../constants'
import { getInstructions, getTransaction, getTransactionAccounts } from '../../utils/solana/transactions'
import { getCloseTokenAccountInstructions } from './utils/accounts'

export type TokenAccountEvents = {
    init: (tokenAccounts: TokenAccountType[]) => void
    add: (tokenAccount: TokenAccountType) => void
    update: (tokenAccount: TokenAccountType) => void
    remove: (tokenAccount: TokenAccountType) => void
    error: (error: unknown) => void
}

export class TokenAccount extends Emitter<TokenAccountEvents, true> {
    protected accounts: Record<string, TokenAccountType> = {}
    protected mints: Record<string, string> = {}
    protected queue = new PQueue({ concurrency: 1, autoStart: false })

    #address?: Address
    #rentExempt?: Lamports

    public constructor(protected readonly wallet: Omit<Wallet, 'tokenAccount'>, protected readonly client: Rpc<SolanaRpcApiTestnet>, protected readonly subscriptions: RpcSubscriptions<SolanaRpcSubscriptionsApi>, protected readonly signal: AbortSignal) {
        super()
    }

    public get address() {
        if (!this.#address) {
            throw new Error('Token account is not initialized')
        }

        return this.#address
    }

    public get tokenAccounts() {
        return Object.values(this.accounts)
    }

    public get rentExempt() {
        if (!this.#rentExempt) {
            throw new Error('Token account is not initialized')
        }

        return this.#rentExempt
    }

    public get(mint: Address) {
        return this.accounts[mint]
    }

    public has(address: Address) {
        return isKeyOf(this.mints, address)
    }

    public hasForMint(mint: Address) {
        return isKeyOf(this.accounts, mint)
    }

    public async init(address: Address, emit = true) {
        this.queue.pause()
        this.queue.clear()

        await this.queue.onIdle()

        this.#address = address
        this.accounts = {}
        this.mints = {}

        this.wallet.on('logs', (logs) => this.queue.add(() => this.handleTransactionLogs(logs)).catch((error) => {
            this.emit('error', error)
            this.init(this.address)
        }))

        await this.updateRentExempt().then(() => setInterval(() => this.updateRentExempt(), 1000 * 60 * 30))
        await this.watch()
        await this.load()

        this.queue.start()

        if (emit) {
            await this.queue.onIdle().then(() => this.emit('init', this.tokenAccounts))
        }
    }

    protected async updateRentExempt() {
        this.#rentExempt = await this.getRentExempt()
    }

    protected async handleTransactionLogs({ signature, logs, err }: LogsNotification) {
        if (notNullish(err) || logs.every((log) => !log.toLocaleLowerCase().includes('closeaccount'))) {
            return
        }

        const transaction = await getTransaction(this.client, signature)
        const accounts = getTransactionAccounts(transaction)
        const instructions = getInstructions(transaction)
        const closedTokenAccounts = getCloseTokenAccountInstructions(accounts, instructions)

        for (const { address, owner } of closedTokenAccounts) {
            if (owner === this.address && isKeyOf(this.mints, address)) {
                const tokenAccount = this.accounts[this.mints[address]]

                delete this.accounts[this.mints[address]]
                delete this.mints[address]

                this.emit('remove', tokenAccount)
            }
        }
    }

    protected async load() {
        const { value } = await this.client.getTokenAccountsByOwner(this.address, { programId: TOKEN_PROGRAM_ADDRESS }, { commitment: 'confirmed', encoding: 'base64' }).send()

        for (const { pubkey, account } of value) {
            this.handleAccountUpdate({ ...account, data: Buffer.from(account.data[0], 'base64'), programAddress: TOKEN_PROGRAM_ADDRESS, address: pubkey }, false)
        }
    }

    protected async watch() {
        const request = this.subscriptions.programNotifications(TOKEN_PROGRAM_ADDRESS, {
            commitment: 'confirmed',
            encoding: 'base64',
            filters: [
                { dataSize: 165n },
                { memcmp: { encoding: 'base58', offset: 32n, bytes: this.address as unknown as Base58EncodedBytes } },
            ],
        })

        const notifications = await request.subscribe({ abortSignal: this.signal })

        const handler = async () => {
            for await (const { value: { account, pubkey } } of notifications) {
                await this.queue.add(() => this.handleAccountUpdate({ ...account, data: Buffer.from(account.data[0], 'base64'), programAddress: TOKEN_PROGRAM_ADDRESS, address: pubkey }))
            }
        }

        handler().catch((error) => {
            this.emit('error', error)
            this.init(this.address)
        })
    }

    protected handleAccountUpdate(account: Account<Uint8Array>, emit = true) {
        const decoded = this.decodeTokenAccount(account)
        const isExisting = isKeyOf(this.accounts, decoded.mint)

        this.accounts[decoded.mint] = decoded
        this.mints[decoded.address] = decoded.mint

        if (emit) {
            this.emit(isExisting ? 'update' : 'add', decoded)
        }
    }

    protected decodeTokenAccount(account: Account<Uint8Array>): TokenAccountType {
        return transform(decodeToken(account), (decoded) => ({ address: decoded.address, ...pick(decoded.data, 'mint', 'amount') }))
    }

    protected getRentExempt() {
        return this.client.getMinimumBalanceForRentExemption(TOKEN_ACCOUNT_SIZE, { commitment: 'confirmed' }).send()
    }
}
