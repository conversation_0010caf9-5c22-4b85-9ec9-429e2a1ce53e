import type { <PERSON><PERSON>, Nonce as NonceAddress, Rp<PERSON>, RpcSub<PERSON>s, SolanaRpcApiTestnet, SolanaRpcSubscriptionsApi } from '@solana/kit'
import { Emitter } from '@kdt310722/utils/event'
import { transform } from '@kdt310722/utils/function'
import { fetchNonce, getNonceDecoder } from '@solana-program/system'

export interface DurableNonce {
    nonce: NonceAddress
    nonceAccountAddress: Address
    nonceAuthorityAddress: Address
}

export type NonceEvents = {
    error: (error: unknown) => void
    update: (data: DurableNonce) => void
}

export class Nonce extends Emitter<NonceEvents, true> {
    #nonce?: DurableNonce

    public constructor(protected readonly client: Rpc<SolanaRpcApiTestnet>, protected readonly subscriptions: RpcSubscriptions<SolanaRpcSubscriptionsApi>, public readonly address: Address) {
        super()
    }

    public get data() {
        if (!this.#nonce) {
            throw new Error('Nonce is not initialized')
        }

        return this.#nonce
    }

    public async init() {
        await this.watch()

        await fetchNonce(this.client, this.address).then((account) => {
            if (!this.#nonce) {
                this.set({ nonce: account.data.blockhash as unknown as NonceAddress, nonceAccountAddress: this.address, nonceAuthorityAddress: account.data.authority })
            }
        })
    }

    protected async watch() {
        const notifications = await this.subscriptions.accountNotifications(this.address, { commitment: 'confirmed', encoding: 'base64' }).subscribe({ abortSignal: new AbortController().signal })

        const handler = async () => {
            for await (const notification of notifications) {
                transform(getNonceDecoder().decode(Buffer.from(notification.value.data[0], 'base64')), (nonce) => {
                    this.set({ nonce: nonce.blockhash as unknown as NonceAddress, nonceAccountAddress: this.address, nonceAuthorityAddress: nonce.authority })
                })
            }
        }

        handler().catch((error) => {
            this.emit('error', error)
            this.init()
        })
    }

    protected set(data: DurableNonce) {
        this.emit('update', this.#nonce = data)
    }
}
