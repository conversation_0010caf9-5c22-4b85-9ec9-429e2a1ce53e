import { ASSOCIATED_TOKEN_PROGRAM_ADDRESS, TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import { type Address, getAddressDecoder, getAddressEncoder, getProgramDerivedAddress } from '@solana/kit'

export async function generateTokenAccountAddress(owner: Address) {
    const seed = `WSOL_${Math.random().toString(36).slice(2, 15)}_${Date.now().toString()}`

    const buffer = await crypto.subtle.digest(
        'SHA-256',
        Buffer.concat([
            Buffer.from(getAddressEncoder().encode(owner)),
            Buffer.from(seed),
            Buffer.from(getAddressEncoder().encode(TOKEN_PROGRAM_ADDRESS)),
        ]),
    )

    return { seed, address: getAddressDecoder().decode(new Uint8Array(buffer)) }
}

export async function getAssociatedTokenAddress(mint: Address, owner: Address, programId: Address = TOKEN_PROGRAM_ADDRESS, associatedTokenProgramId: Address = ASSOCIATED_TOKEN_PROGRAM_ADDRESS) {
    const encoder = getAddressEncoder()
    const seeds = [encoder.encode(owner), encoder.encode(programId), encoder.encode(mint)]

    return getProgramDerivedAddress({ programAddress: associatedTokenProgramId, seeds }).then(([address]) => address)
}
