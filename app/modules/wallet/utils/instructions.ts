import type { Lamports, TransactionSigner } from '@solana/kit'
import { getCreateAccountWithSeedInstruction } from '@solana-program/system'
import { getInitializeAccount3Instruction, TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import { NATIVE_MINT, TOKEN_ACCOUNT_SIZE } from '../../../constants'
import { generateTokenAccountAddress } from './addresses'

export async function getCreateWSolTokenAccountInstructions(owner: TransactionSigner, amount: bigint | Lamports, rentExempt: bigint | Lamports) {
    const { seed, address } = await generateTokenAccountAddress(owner.address)

    const instructions = [
        getCreateAccountWithSeedInstruction({
            payer: owner,
            newAccount: address,
            base: owner.address,
            baseAccount: owner,
            seed,
            amount: amount + rentExempt,
            space: TOKEN_ACCOUNT_SIZE,
            programAddress: TOKEN_PROGRAM_ADDRESS,
        }),
        getInitializeAccount3Instruction({
            account: address,
            mint: NATIVE_MINT,
            owner: owner.address,
        }),
    ]

    return { address, seed, instructions }
}
