import type { GetTransactionResponse } from '../../../utils/solana/transactions'
import type { TradeParam } from '../../raydium/trade'
import { escapeMarkdownV2 } from '@kdt310722/logger'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { stringifyJson } from '@kdt310722/utils/json'
import { formatDate } from '@kdt310722/utils/time'
import { tokenLink } from '../utils/messages'
import { getTokenHelpLinks } from './transaction'

export interface BuyNotificationData {
    transaction: GetTransactionResponse
    params: TradeParam
}

export function getBuyNotificationMessage({ transaction, params }: BuyNotificationData, name?: string) {
    const message: string[] = []
    const isBuySuccess = isNullish(transaction.meta?.err)

    if (isBuySuccess) {
        message.push(`🚀 *Buy Alert* 🚀`)
    } else {
        message.push(`❌ *Buy Failed* ❌`)
    }

    message.push('')

    if (notNullish(name)) {
        message.push(` \\- Name: *${name}*`)
    }

    message.push(
        ` \\- Token: *${tokenLink(params.pool.baseMint, params.pool.baseMint.slice(0, 4))}*`,
        ` \\- Time: *${escapeMarkdownV2(formatDate(new Date(), true))}*`,
    )

    if (notNullish(transaction.meta?.err)) {
        message.push(`\`\`\`json\n${escapeMarkdownV2(stringifyJson(transaction.meta.err, { indent: 2 }))}\`\`\``)
    }

    message.push('', getTokenHelpLinks(params.pool.baseMint))

    return message.join('\n')
}
