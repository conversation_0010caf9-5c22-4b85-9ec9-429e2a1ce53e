import type { PoolState } from '@kdt-bun/raydium-launchpad-sdk'
import type { DexScreenerPaymentTransaction } from '../../../types/tracker'
import { escapeMarkdownV2 } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { transform } from '@kdt310722/utils/function'
import { formatNanoseconds } from '@kdt310722/utils/number'
import { formatDate } from '@kdt310722/utils/time'
import { TOKEN_HELP_LINKS } from '../constants'
import { accountLink, link, tokenLink, txLink } from '../utils/messages'

export interface PaymentTransactionNotificationData {
    transaction: DexScreenerPaymentTransaction
    pools: PoolState[]
}

export function getTokenHelpLinks(mint: string) {
    const links = Object.entries(TOKEN_HELP_LINKS)
    const message: string[] = []

    for (const [label, url] of links) {
        message.push(link(url.replaceAll('__MINT__', mint), label))
    }

    return message.join(String.raw` \| `)
}

export function formatToken(pool: PoolState) {
    return [`  \\- *${tokenLink(pool.baseMint, pool.baseMint.slice(0, 4))}*`, ` \\- ${getTokenHelpLinks(pool.baseMint)}`].join('')
}

export function formatTokens(pools: PoolState[]) {
    return pools.map((pool) => formatToken(pool)).join('\n')
}

export function getPaymentTransactionNotificationMessage({ transaction, pools }: PaymentTransactionNotificationData) {
    const message: string[] = []

    message.push(
        `Transaction: *${txLink(transaction.signature)}*`,
        `Slot: *${transaction.slot}*`,
        `Source: *${escapeMarkdownV2(transaction.datasource)}*`,
        `Account: *${accountLink(transaction.payer)}*`,
        `Received At: *${escapeMarkdownV2(formatDate(new Date(transaction.receivedAt), true))}*`,
    )

    if (notNullish(transaction.blockTime)) {
        message.push(`Block Time: *${escapeMarkdownV2(formatDate(new Date(transaction.blockTime * 1000), true))}*`)
    }

    if (notNullish(transaction.nodeTime)) {
        message.push(
            `Node Time: *${escapeMarkdownV2(formatDate(new Date(transaction.nodeTime), true))}*`,
            `Latency: *${escapeMarkdownV2(transform(formatNanoseconds(BigInt((transaction.receivedAt - transaction.nodeTime) * 1e6)), (val) => (val.length === 0 ? '0ms' : val)))}*`,
        )
    }

    if (pools.length > 0) {
        message.push('Tokens:', formatTokens(pools))
    }

    return message.join('\n')
}
