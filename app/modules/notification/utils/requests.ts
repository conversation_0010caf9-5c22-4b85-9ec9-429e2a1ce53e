import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type RetryOptions, withRetry, withTimeout } from '@kdt310722/utils/promise'

export interface PostOptions {
    headers?: Record<string, string>
    timeout?: number
    retry?: boolean | (RetryOptions & { enabled?: boolean })
}

export async function post(url: string, data: any, { headers = {}, timeout = 10_000, retry = true }: PostOptions = {}) {
    const createError = (message: string, cause?: unknown) => Object.assign(new Error(message, { cause }), { url, data, headers })
    const retryOptions = resolveNestedOptions(retry) || { enabled: false }

    const execute = async () => {
        const response = await withTimeout(fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', ...headers }, body: JSON.stringify(data) }), timeout, () => createError('Request timeout'))
        const responseText = await response.text()

        if (!response.ok) {
            throw Object.assign(createError(`Request failed with status code ${response.status} (${response.statusText})`), { response: { body: responseText, headers: response.headers } })
        }

        return JSON.parse(responseText)
    }

    return retryOptions.enabled ? withRetry(execute, retryOptions) : execute()
}
