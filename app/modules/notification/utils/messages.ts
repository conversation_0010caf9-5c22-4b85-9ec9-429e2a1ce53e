import { escapeMarkdownV2 } from '@kdt310722/logger'
import { shorten } from '@kdt310722/utils/string'

export function link(url: string, label: string) {
    return `[${escapeMarkdownV2(label)}](${url})`
}

export function txLink(tx: string, label?: string) {
    return link(`https://solscan.io/tx/${tx}`, label ?? shorten(tx, 4))
}

export function accountLink(account: string) {
    return link(`https://solscan.io/account/${account}`, shorten(account, 4))
}

export function tokenLink(mint: string, name: string) {
    return link(`https://solscan.io/token/${mint}`, name)
}
