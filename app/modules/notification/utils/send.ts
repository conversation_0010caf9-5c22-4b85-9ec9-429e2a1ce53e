import type { AnyObject } from '@kdt310722/utils/object'
import { post } from './requests'

export async function send(botToken: string, chatId: number | string, message: string, options: AnyObject = {}) {
    const payload = { chat_id: chatId, text: message, parse_mode: 'MarkdownV2', link_preview_options: { is_disabled: true, ...options.link_preview_options }, ...options }
    const result = await post(`https://api.telegram.org/bot${botToken}/sendMessage`, payload)

    if (!result.ok) {
        throw Object.assign(new Error('Failed to send message to Telegram'), { payload, response: result })
    }
}

export interface NtfyNotification {
    link?: string
    title?: string
    priority?: 1 | 2 | 3 | 4 | 5
}

export async function sendNtfyNotification(topic: string, message: string, { link, title, priority = 3 }: NtfyNotification = {}) {
    const payload = {
        topic,
        message,
        click: link,
        title,
        priority,
        markdown: true,
    }

    await post(`https://ntfy.sh`, payload)
}
