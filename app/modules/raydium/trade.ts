import type { Wallet } from '../wallet/wallet'
import type { Raydium } from './raydium'
import { CurveType, getTradeInstruction, type PoolState, type TradeParams as RaydiumTradeParams, TradeType } from '@kdt-bun/raydium-launchpad-sdk'
import { appendTransactionMessageInstruction, appendTransactionMessageInstructions, createTransactionMessage, pipe, setTransactionMessageFeePayer, setTransactionMessageLifetimeUsingDurableNonce } from '@solana/kit'
import { prepareForTrade } from '../../utils/raydium/trade'
import { setTransactionMessagePriorityFee } from '../../utils/solana/transaction-messages'

export enum TradeDirection {
    Buy,
    Sell,
}

export interface TradeParam {
    pool: PoolState
    amount: bigint
    slippage: number
    priorityFee: bigint
}

export class Trade {
    public constructor(protected readonly raydium: Omit<Raydium, 'trade'>, protected readonly wallet: Wallet) {}

    public async getBuyTransaction(param: TradeParam) {
        return this.buildTradeTransaction(TradeDirection.Buy, param)
    }

    public async getSellTransaction(param: TradeParam) {
        return this.buildTradeTransaction(TradeDirection.Sell, param)
    }

    public async buildTradeTransaction(direction: TradeDirection, param: TradeParam) {
        const { quoteTokenAccount, baseTokenAccount, preInstructions, postInstructions } = await prepareForTrade(this.wallet, param.pool.baseMint, param.amount)

        const tradeParams: RaydiumTradeParams = {
            type: TradeType.ExactIn,
            curveType: CurveType.Constant,
            payer: this.wallet.signer,
            pool: param.pool,
            tokenIn: direction === TradeDirection.Buy ? param.pool.quoteMint : param.pool.baseMint,
            baseTokenAccount,
            quoteTokenAccount,
            amount: param.amount,
            slippage: param.slippage,
            shareFeeRate: 0n,
            fee: this.raydium.fee,
        }

        const instruction = await getTradeInstruction(tradeParams)

        return pipe(
            createTransactionMessage({ version: 'legacy' }),
            (tx) => setTransactionMessageFeePayer(this.wallet.signer.address, tx),
            (tx) => setTransactionMessageLifetimeUsingDurableNonce(this.wallet.nonce.data, tx),
            (tx) => setTransactionMessagePriorityFee({ units: 100_000, lamports: param.priorityFee }, tx),
            (tx) => appendTransactionMessageInstructions(preInstructions, tx),
            (tx) => appendTransactionMessageInstruction(instruction, tx),
            (tx) => appendTransactionMessageInstructions(postInstructions, tx),
        )
    }
}
