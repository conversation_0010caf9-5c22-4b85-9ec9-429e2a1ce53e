import type { Address, Rpc, RpcSubscriptions, SolanaRpcApiTestnet, SolanaRpcSubscriptionsApi } from '@solana/kit'
import type { Wallet } from '../wallet/wallet'
import { Emitter } from '@kdt310722/utils/event'
import { GlobalConfig } from './global-config'
import { PlatformConfig } from './platform-config'
import { Trade } from './trade'

export type RaydiumEvents = {
    fee: (fee: bigint) => void
}

export class Raydium extends Emitter<RaydiumEvents, true> {
    public readonly globalConfig: GlobalConfig
    public readonly platformConfig: PlatformConfig
    public readonly trade: Trade

    public constructor(protected readonly client: Rpc<SolanaRpcApiTestnet>, protected readonly subscriptions: RpcSubscriptions<SolanaRpcSubscriptionsApi>, globalConfigAddress: Address, platformConfigAddress: Address, wallet: Wallet) {
        super()

        this.globalConfig = new GlobalConfig(this.client, this.subscriptions, globalConfigAddress)
        this.platformConfig = new PlatformConfig(this.client, this.subscriptions, platformConfigAddress)
        this.trade = new Trade(this, wallet)

        this.globalConfig.on('update', () => this.updateFee())
        this.platformConfig.on('update', () => this.updateFee())
    }

    public get fee() {
        return this.globalConfig.data.tradeFeeRate + this.platformConfig.data.feeRate
    }

    public async init() {
        await this.globalConfig.init()
        await this.platformConfig.init()
    }

    protected updateFee() {
        try {
            this.emit('fee', this.fee)
        } catch {}
    }
}
