import type { Address, Rpc, RpcSub<PERSON>s, SolanaRpcApiTestnet, SolanaRpcSubscriptionsApi } from '@solana/kit'
import { Emitter } from '@kdt310722/utils/event'
import { decodePlatformConfig, fetchPlatformConfig, type PlatformConfig as PlatformConfigType, RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS } from '@kdt-bun/raydium-launchpad-sdk'

export type PlatformConfigEvents = {
    error: (error: unknown) => void
    update: (data: PlatformConfigType) => void
}

export class PlatformConfig extends Emitter<PlatformConfigEvents, true> {
    #platformConfig?: PlatformConfigType

    public constructor(protected readonly client: Rpc<SolanaRpcApiTestnet>, protected readonly subscriptions: RpcSubscriptions<SolanaRpcSubscriptionsApi>, public readonly address: Address) {
        super()
    }

    public get data() {
        if (!this.#platformConfig) {
            throw new Error('Platform config is not initialized')
        }

        return this.#platformConfig
    }

    public async init() {
        await this.watch()

        await fetchPlatformConfig(this.client, this.address).then((account) => {
            if (!this.#platformConfig) {
                this.set(account.data)
            }
        })
    }

    protected async watch() {
        const notifications = await this.subscriptions.accountNotifications(this.address, { commitment: 'confirmed', encoding: 'base64' }).subscribe({ abortSignal: new AbortController().signal })

        const handler = async () => {
            for await (const notification of notifications) {
                this.set(decodePlatformConfig({ ...notification.value, address: this.address, data: Buffer.from(notification.value.data[0], 'base64'), programAddress: RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS }).data)
            }
        }

        handler().catch((error) => {
            this.emit('error', error)
            this.init()
        })
    }

    protected set(data: PlatformConfigType) {
        this.emit('update', this.#platformConfig = data)
    }
}
