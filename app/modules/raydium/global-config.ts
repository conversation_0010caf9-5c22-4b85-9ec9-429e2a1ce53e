import type { Address, Rpc, RpcSub<PERSON>s, SolanaRpcApiTestnet, SolanaRpcSubscriptionsApi } from '@solana/kit'
import { Emitter } from '@kdt310722/utils/event'
import { decodeGlobalConfig, fetchGlobalConfig, type GlobalConfig as GlobalConfigType, RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS } from '@kdt-bun/raydium-launchpad-sdk'

export type GlobalConfigEvents = {
    error: (error: unknown) => void
    update: (data: GlobalConfigType) => void
}

export class GlobalConfig extends Emitter<GlobalConfigEvents, true> {
    #globalConfig?: GlobalConfigType

    public constructor(protected readonly client: Rpc<SolanaRpcApiTestnet>, protected readonly subscriptions: RpcSubscriptions<SolanaRpcSubscriptionsApi>, public readonly address: Address) {
        super()
    }

    public get data() {
        if (!this.#globalConfig) {
            throw new Error('Global config is not initialized')
        }

        return this.#globalConfig
    }

    public async init() {
        await this.watch()

        await fetchGlobalConfig(this.client, this.address).then((account) => {
            if (!this.#globalConfig) {
                this.set(account.data)
            }
        })
    }

    protected async watch() {
        const notifications = await this.subscriptions.accountNotifications(this.address, { commitment: 'confirmed', encoding: 'base64' }).subscribe({ abortSignal: new AbortController().signal })

        const handler = async () => {
            for await (const notification of notifications) {
                this.set(decodeGlobalConfig({ ...notification.value, address: this.address, data: Buffer.from(notification.value.data[0], 'base64'), programAddress: RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS }).data)
            }
        }

        handler().catch((error) => {
            this.emit('error', error)
            this.init()
        })
    }

    protected set(data: GlobalConfigType) {
        this.emit('update', this.#globalConfig = data)
    }
}
