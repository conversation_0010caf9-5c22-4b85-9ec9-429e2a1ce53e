import z from 'zod'
import { baseTokenAmount, solAmount } from '../utils/schemas/amounts'

export const swap = z.object({
    slippage: z.number().nonnegative(),
    priorityFee: solAmount,
    timeout: z.number().int().positive().default(5000),
})

export const buy = swap.extend({
    enabled: z.boolean().default(true),
    amount: solAmount,
    minBalance: solAmount,
    minHolding: baseTokenAmount.default(0),
    minMarketCap: z.coerce.number().safe().finite().nonnegative().default(10_000),
    maxMarketCap: z.coerce.number().safe().finite().nonnegative().default(50_000),
})
