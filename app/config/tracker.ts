import { isString } from '@kdt310722/utils/string'
import z from 'zod'
import { nullish } from '../utils/schemas/nullish'
import { wsUrl } from '../utils/schemas/requests'

const timeout = z.object({
    connect: z.number().int().positive().default(5000),
    disconnect: z.number().int().positive().default(5000),
    request: z.number().int().positive().default(30_000),
})

const reconnect = z.object({
    enable: z.boolean().default(true),
    delay: z.number().int().nonnegative().default(1000),
    attempts: z.number().int().positive().default(5),
})

const heartbeat = z.object({
    enable: z.boolean().default(true),
    interval: z.number().int().positive().default(30_000),
    timeout: z.number().int().positive().default(10_000),
})

export const websocketSchema = z.object({
    url: wsUrl,
    protocols: nullish(z.union([z.string().nonempty(), z.string().nonempty().array()])),
    timeout: timeout.default({}).transform((val) => ({ connectTimeout: val.connect, disconnectTimeout: val.disconnect, sendTimeout: val.request, requestTimeout: val.request })),
    reconnect: reconnect.default({}),
    heartbeat: heartbeat.default({}),
})

export const websocket = z.union([wsUrl, websocketSchema]).transform((val) => {
    return isString(val) ? websocketSchema.parse({ url: val }) : val
})

export const tracker = websocket
