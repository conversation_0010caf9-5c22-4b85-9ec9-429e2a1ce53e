import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { httpUrl, wsUrl } from '../utils/schemas/requests'
import { retry } from '../utils/schemas/retry'

const httpSchema = z.object({
    url: httpUrl,
    timeout: z.number().int().nonnegative().default(30_000),
    retry,
    headers: z.record(z.string(), z.string()).default({}),
})

export const rpcHttpClient = z.union([httpUrl, httpSchema]).transform((val) => {
    return isString(val) ? httpSchema.parse({ url: val }) : val
})

export const rpcClient = z.object({
    http: rpcHttpClient,
    send: rpcHttpClient.nullish(),
    ws: wsUrl,
})
