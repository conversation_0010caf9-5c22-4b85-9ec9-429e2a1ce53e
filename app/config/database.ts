import { isString, trim } from '@kdt310722/utils/string'
import { z } from 'zod'
import { storagePath } from '../utils/path'
import { bool } from '../utils/schemas/bool'

const levels = ['query', 'schema', 'error', 'warn', 'info', 'log', 'migration'] as const
const logging = z.preprocess((value) => (isString(value) ? (value === 'all' ? levels : value.split(',').map((i) => trim(i))) : value), z.enum(levels).array())

const base = z.object({
    logging: logging.default(['error', 'warn', 'migration']),
    maxQueryExecutionTime: z.coerce.number().int().positive().default(100),
    dropSchema: bool.default(false),
    synchronize: bool.default(true),
    defaultChunkSize: z.coerce.number().int().positive().default(100),
})

const postgres = base.extend({
    type: z.literal('postgres'),
    host: z.string().default('localhost'),
    port: z.coerce.number().int().positive().default(5432),
    username: z.string().default('postgres'),
    password: z.string().default('postgres'),
    database: z.string(),
    schema: z.string().default('public'),
})

const sqlite = base.extend({
    type: z.literal('sqlite').transform((): 'better-sqlite3' => 'better-sqlite3'),
    database: z.string().nonempty().default(storagePath('database.sqlite3')),
})

export const database = z.discriminatedUnion('type', [postgres, sqlite]).default({ type: 'sqlite' })
