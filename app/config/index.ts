import { createConfig } from '../utils/config'
import { buy } from './buy'
import { database } from './database'
import { indexer } from './indexer'
import { logger } from './logger'
import { notification } from './notification'
import { raydium } from './raydium'
import { rpcClient } from './rpc-client'
import { tracker } from './tracker'
import { wallet } from './wallet'

export const config = createConfig({
    logger,
    database,
    rpcClient,
    tracker,
    buy,
    wallet,
    raydium,
    notification,
    indexer,
})
