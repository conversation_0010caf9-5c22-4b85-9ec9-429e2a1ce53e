{"type": "module", "private": true, "packageManager": "pnpm@10.13.1", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "UNLICENSED", "engines": {"node": ">=22.16.0"}, "scripts": {"dev": "node bin/run.js", "start": "NODE_ENV=production node bin/run.js", "up": "ncu -i -x zod -x zod-validation-error", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks"}, "dependencies": {"@kdt-bun/raydium-launchpad-sdk": "^0.0.1", "@kdt310722/config": "^0.0.4", "@kdt310722/logger": "^0.0.12", "@kdt310722/rpc": "^0.2.1", "@kdt310722/utils": "^0.0.19", "@solana-program/compute-budget": "^0.8.0", "@solana-program/system": "^0.7.0", "@solana-program/token": "^0.5.1", "@solana/kit": "^2.3.0", "@swc-node/register": "^1.10.10", "better-sqlite3": "^12.2.0", "bs58": "^6.0.0", "decimal.js": "^10.6.0", "p-queue": "^8.1.0", "pluralize": "^8.0.0", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25", "typeorm-naming-strategies": "^4.1.0", "zod": "3.25.76", "zod-validation-error": "3.5.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt-farm/eslint-config": "^0.0.2", "@kdt310722/tsconfig": "^1.0.0", "@types/node": "^24.1.0", "@types/pluralize": "^0.0.33", "eslint": "^9.31.0", "lint-staged": "^16.1.2", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx tsc --noEmit && npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}